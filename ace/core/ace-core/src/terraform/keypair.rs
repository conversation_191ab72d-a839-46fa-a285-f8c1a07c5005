use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetAllKeypairs,
}

/// Generates keypair configs for all keypairs within the environment EXCEPT for the ACE keypair.
pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let account_key = &config.account_key;
    let mut key_names: Vec<String> = Vec::new();
    let mut keypairs = vec![];

    // Get all keypairs
    keypairs.extend(
        ace_graph::keypair::select(&ace_graph::KeyPairFilter::All, ace_db_app)
            .await
            .change_context(ErrorStack::GetAllKeypairs)?,
    );

    // This prevents non-deterministic ordering in the output
    keypairs.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    // Write to output
    for keypair in keypairs {
        // Skip ACE keypair
        if matches!(&keypair.graphkey, &ace_graph::KeyPair::Ace) {
            continue;
        }

        let key_name = format!("{}-{}", account_key, keypair.name);

        key_names.push(key_name.clone());

        #[rustfmt::skip]
        output.write(&Some(keypair.name.to_string()),&CNSL!(r#"

            resource "aws_key_pair" "#, JE!(&key_name), r#" {
                key_name   = "#, JE!(&key_name), r#"
                public_key = "#, JE!(&keypair.public_key), r#"

                tags = {
                    Source = "Terraform"
                    AccountKey = "#, JE!(&account_key), r#"
                }
            }
        "#));
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}
