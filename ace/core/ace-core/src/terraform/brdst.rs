use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use serde_json::json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetBrdsts,
}

fn acrm1_extras(bucket: &str) -> Vec<serde_json::Value> {
    // This has to do with cloudconvert being able to write files to this bucket

    let bucket_wildcard = format!("arn:aws:s3:::{bucket}/*");
    let bucket_atsymbol = format!("arn:aws:s3:::{bucket}/@*");
    let bucket_itself = format!("arn:aws:s3:::{bucket}");

    let mut rval = vec![];

    rval.push(json!(
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::395540211253:root"
            },
            "Action": [
                "s3:GetObjectAcl",
                "s3:GetObject",
                "s3:PutObjectAcl",
                "s3:PutObject",
                "s3:ListMultipartUploadParts"
            ],
            "Resource": bucket_wildcard
        }
    ));

    rval.push(json!(
        {
            "Effect": "Deny",
            "Principal": {
                "AWS": "arn:aws:iam::395540211253:root"
            },
            "Action": [
                "s3:GetObjectAcl",
                "s3:GetObject",
                "s3:PutObjectAcl",
                "s3:PutObject",
                "s3:ListMultipartUploadParts"
            ],
            "Resource": bucket_atsymbol
        }
    ));

    rval.push(json!(
        {
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::395540211253:root"
            },
            "Action": [
                "s3:ListBucketMultipartUploads",
                "s3:GetBucketLocation"
            ],
            "Resource": bucket_itself,
        }
    ));

    rval
}

pub async fn generate(
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut sorted_targets = ace_graph::brdst::select(&ace_graph::BrdstFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetBrdsts)?;

    // This prevents non-deterministic ordering in the output
    sorted_targets.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    for target in sorted_targets {
        let resource_name = format!("{}-brtar", target.name);

        let target_bucket = &target.target.bucket;
        let target_bucket_arn = &target.target.bucket_arn;

        // Very important that this matches the code that generates it the source side
        let source_iam_role_name = format!(
            "{}-{}-{}-brsrc",
            target.source.account_key, target.source.region, target.name
        );

        let source_aws_account_id = &target.source.aws_account_id;

        let identifier =
            format!("arn:aws:iam::{source_aws_account_id}:role/{source_iam_role_name}");

        let mut statements = vec![];

        statements.push(json!(
            {
                "Sid":"Permissions on objects",
                "Effect":"Allow",
                "Principal":{
                    "AWS": identifier
                },
                "Action":[
                    "s3:ReplicateDelete",
                    "s3:ReplicateObject"
                ],
                "Resource": format!("{}/*", target_bucket_arn)
            }
        ));

        statements.push(json!(
            {
                "Sid":"Permissions on bucket",
                "Effect":"Allow",
                "Principal":{
                    "AWS": identifier
                },
                "Action": [
                    "s3:List*",
                    "s3:GetBucketVersioning",
                    "s3:PutBucketVersioning"
                ],
                "Resource": target_bucket_arn
            }
        ));

        statements.extend(acrm1_extras(target_bucket));

        #[rustfmt::skip]
        output.write(&Some(target.name.to_string()),&CNSL!(r#"
            # ---
            # brdst for 
            # name: "#, JE!(target.name), r#"
            # source.bucket: "#, (target.source.bucket), r#"
            # target.bucket: "#, (target.target.bucket), r#"

            resource "aws_s3_bucket_policy" "#, JE!(resource_name), r#" {
                bucket = "#, JE!(target_bucket), r#"
                policy = jsonencode("#, JE!(json!(
                    {
                        "Version":"2012-10-17",
                        "Id":"PolicyForDestinationBucket",
                        "Statement": statements
                    }
                )), r#")
            }
        "#));
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}
