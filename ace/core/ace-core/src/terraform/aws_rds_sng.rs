use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetAwsRdsSubnetGroups,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut sorted_aws_rds_sngs =
        ace_graph::aws_rds_sng::select(ace_graph::AwsRdsSngFilter::All, ace_db_app)
            .await
            .change_context(ErrorStack::GetAwsRdsSubnetGroups)?;

    // This prevents non-deterministic ordering in the output
    sorted_aws_rds_sngs.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    for aws_rds_sng in sorted_aws_rds_sngs {
        let resource_id = aws_rds_sng.graphkey.serialize_dashed();

        let subnet_ids = aws_rds_sng
            .subnets
            .iter()
            .map(|subnet| format!("aws_subnet.{}.id", subnet.to_old_terraform_resource_name()))
            .collect::<Vec<_>>();

        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            resource "aws_db_subnet_group" "#, JE!(resource_id), r#" {
                name = "#, JE!(aws_rds_sng.name), r#"
                subnet_ids = ["#, (subnet_ids.join(",")), r#"]

                tags = {
                    Name = "#, JE!(aws_rds_sng.name), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&config.account_key), r#"
                }
            }
        "#));
    }

    Ok(())
}
