use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetAwsElbNetworkLoadBalancers,
    GetSecurityGroups,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut sorted_elbs = ace_graph::aws_elb::select(ace_graph::AwsElbFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetAwsElbNetworkLoadBalancers)?;

    // This prevents non-deterministic ordering in the output
    sorted_elbs.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    for elb in sorted_elbs {
        let resource_id = elb.graphkey.serialize_dashed();

        let security_groups = ace_graph::aws_vpc_sg::select(
            &ace_graph::AwsVpcSecurityGroupFilter::One(ace_graph::AwsVpcSg::AwsElb(
                elb.graphkey.clone(),
            )),
            ace_db_app,
        )
        .await
        .change_context(ErrorStack::GetSecurityGroups)?
        .into_iter()
        .map(|sg| {
            format!(
                "aws_security_group.{}.id",
                sg.to_terraform_resource_id(config)
            )
        })
        .collect::<Vec<_>>();

        let subnet_ids = elb
            .subnets
            .iter()
            .map(|subnet| format!("aws_subnet.{}.id", subnet.to_old_terraform_resource_name()))
            .collect::<Vec<_>>();

        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            resource "aws_lb" "#, JE!(resource_id), r#" {
                name = "#, JE!(elb.name), r#"
                internal = false
                load_balancer_type = "network"
                subnets = ["#, (subnet_ids.join(",")), r#"]
                enable_cross_zone_load_balancing = "#, JE!(elb.enable_cross_zone_load_balancing), r#"
                security_groups = ["#, (security_groups.join(",")), r#"]

                tags = {
                    Name = "#, JE!(elb.name), r#"
                    Source = "Terraform"
                    AccountKey = "#, JE!(&config.account_key), r#"
                    Purpose = "#, JE!(elb.purpose), r#"
                }
            }

            output "#, (format!("{}_dns_name", resource_id)), r#" {
                value = aws_lb."#, (resource_id), r#".dns_name
            }
        "#));
    }

    Ok(())
}
