use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use hcl::Block;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetEcrs,
    ConvertToHcl,
}

pub async fn generate(
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut ecrs = ace_graph::ecr::select(&ace_graph::EcrFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetEcrs)?;

    // This prevents non-deterministic ordering in the output
    ecrs.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    let blocks: Vec<Block> = ecrs
        .iter()
        .map(|ecr| {
            hcl::block!(
                resource "aws_ecr_repository" (ecr.graphkey.serialize_dashed()) {
                    name = (ecr.name)
                    image_tag_mutability = (ecr.image_tag_mutability)

                    image_scanning_configuration {
                        scan_on_push = (ecr.scan_on_push)
                    }
                }
            )
        })
        .collect();

    let body = hcl::Body::from_iter(blocks);
    let body_string = hcl::to_string(&body)
        .change_context(ErrorStack::ConvertToHcl)?
        .replace("$$$", "$");
    output.write(&None, &body_string);

    Ok(())
}
