use std::vec;

use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};
use serde_json::json;

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    ConvertToHcl,
    GetApps,
    GetIamRole,
    SelectBuckets,
}

pub async fn generate(
    config: &ace_graph::config::Config,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut sorted_apps = ace_graph::app::select(&ace_graph::AppFilter::All, ace_db_app)
        .await
        .change_context(ErrorStack::GetApps)?;

    // This prevents non-deterministic ordering in the output
    sorted_apps.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    for app in sorted_apps {
        generate_one(config, &app, output, ace_db_app).await?;
    }

    //output.add_output_pair("key_names", key_names.join(sep));

    Ok(())
}

async fn generate_one(
    config: &ace_graph::config::Config,
    app: &ace_graph::app::App,
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut blocks = vec![];
    let main_resource = format!("app-{}", app.name);
    let user_name = format!("{}-{}-app-{}", config.account_key, config.region, app.name);

    let mut iam_policy_statements = vec![];

    // Get buckets for this app (to create policy statements)
    let filter =
        ace_graph::BucketFilter::One(ace_graph::Bucket::App(app.graphkey.clone(), "".to_string()));

    for bucket in ace_graph::bucket::select(filter, ace_db_app)
        .await
        .change_context(ErrorStack::SelectBuckets)?
    {
        let bucket_name = &bucket.name;

        iam_policy_statements.push(json!(
            {
                "Action": [
                    "s3:ListBucket",
                    "s3:ListBucketMultipartUploads",
                    "s3:GetBucketLocation"
                ],
                "Effect": "Allow",
                "Resource": [
                    (format!("arn:aws:s3:::{bucket_name}"))
                ]
            }
        ));

        iam_policy_statements.push(json!(
            {
                "Action": [
                    "s3:PutObject",
                    "s3:GetObject",
                    "s3:DeleteObject",
                    "s3:AbortMultipartUpload",
                    "s3:ListMultipartUploadParts"
                ],
                "Effect": "Allow",
                "Resource": [
                    (format!("arn:aws:s3:::{bucket_name}/*"))
                ]
            }
        ));
    }

    // setup policy if it's not empty
    if !iam_policy_statements.is_empty() {
        blocks.push(hcl::block!(
            resource "aws_iam_policy" (&main_resource) {
                name = (&main_resource)
                policy = (json!({
                    "Version": "2012-10-17",
                    "Statement": iam_policy_statements
                }).to_string())
            }
        ));
    }

    // Create/write the iam_access_key block raw...because the hcl crate doesn't let you insert unquoted values...
    #[rustfmt::skip]
    output.write(&Some(app.name.clone()), &CNSL!(r#"
        resource "aws_iam_access_key" "#, JE!(main_resource), r#" {
            user = "#, JE!(user_name) , r#"
            depends_on = [aws_iam_user."#, (main_resource), r#"]
        }

    "#));

    // attach policy to role if it's not empty
    if !iam_policy_statements.is_empty() {
        // Gonna have to get the actual IamRole here so we don't have TWO places the role name is being created.
        let iam_role =
            ace_graph::iam_role::get(ace_graph::IamRole::App(app.graphkey.clone()), ace_db_app)
                .await
                .change_context(ErrorStack::GetIamRole)?;

        let (_, role_name, _, _) = iam_role.terraform_resource(config);

        blocks.push(hcl::block!(
            resource "aws_iam_role_policy_attachment" (&main_resource) {
                role = (&role_name)
                policy_arn = (format!("$${{aws_iam_policy.{main_resource}.arn}}"))
            }
        ));
    }

    // create an iam user
    blocks.push(hcl::block!(
        resource "aws_iam_user" (&main_resource) {
            name = (&user_name)
            tags = {
                Name = (&user_name)
                Source = "Terraform"
                AccountKey = (&config.account_key)
            }
        }
    ));

    // attach policy to user if there is a policy
    if !iam_policy_statements.is_empty() {
        blocks.push(hcl::block!(
            resource "aws_iam_user_policy_attachment" (&main_resource) {
                user = (&user_name)
                policy_arn = (format!("$${{aws_iam_policy.{main_resource}.arn}}"))
            }
        ));
    }

    // add outputs for this user's key and secret
    blocks.push(hcl::block!(
        output (format!("{}-user-access-key-id", main_resource)) {
            value = (format!("$${{aws_iam_access_key.{main_resource}.id}}"))
            sensitive = true
        }
    ));
    blocks.push(hcl::block!(
        output (format!("{}-user-secret-access-key", main_resource)) {
            value = (format!("$${{aws_iam_access_key.{main_resource}.secret}}"))
            sensitive = true
        }
    ));

    let body = hcl::Body::from_iter(blocks);
    let body_string = hcl::to_string(&body)
        .change_context(ErrorStack::ConvertToHcl)?
        .replace("$$$", "$");
    output.write(&Some(app.name.clone()), &body_string);
    Ok(())
}
