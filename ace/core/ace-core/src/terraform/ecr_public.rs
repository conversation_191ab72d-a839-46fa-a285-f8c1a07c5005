use super::Output;
use ace_graph::GraphKeyExt;
use error_stack::ResultExt;
use garbage::{CNSL, JE};

#[derive(Debug, ace_proc::ErrorStack)]
pub enum ErrorStack {
    GetPublicEcrs,
}

pub async fn generate(
    output: &mut Output,
    ace_db_app: &ace_db::App,
) -> error_stack::Result<(), ErrorStack> {
    let mut public_ecrs =
        ace_graph::ecr_public::select(&ace_graph::EcrPublicFilter::All, ace_db_app)
            .await
            .change_context(ErrorStack::GetPublicEcrs)?;

    // This prevents non-deterministic ordering in the output
    public_ecrs.sort_by(|a, b| a.graphkey.serialize().cmp(&b.graphkey.serialize()));

    // Set region (of creation) for public ECRs
    if !public_ecrs.is_empty() {
        output.write(
            &None,
            &CNSL!(
                r#"
            provider "aws" {
                alias = "us-east-1"
                region = "us-east-1"
            }
        "#
            ),
        );
    }

    for ecr_pub in public_ecrs {
        #[rustfmt::skip]
        output.write(&None, &CNSL!(r#"
            resource "aws_ecrpublic_repository" "#, JE!(ecr_pub.graphkey.serialize_dashed()), r#" {
                provider = aws.us-east-1
                repository_name = "#, JE!(ecr_pub.repository_name), r#"
            }
        "#));
    }

    Ok(())
}
